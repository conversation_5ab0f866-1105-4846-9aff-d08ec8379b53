from fastapi import APIRouter, WebSocket, WebSocketDisconnect, UploadFile, File, HTTPException
from app.managers.ProjectManager import ProjectManager
from app.utils.csv_handler import analyze_csv, save_csv_metadata, create_csv_summary_for_rag, generate_csv_analysis_code
import tempfile
import os

router = APIRouter()

@router.websocket("/ws/project/{project_id}")
async def project_ws(websocket: WebSocket, project_id: str):
    project = ProjectManager.from_project_id(project_id, base_dir="./projects")
    if not project:
        await websocket.accept()
        await websocket.send_json({"error": "Project does not exist or is unavailable."})
        await websocket.close(code=4001)
        return
    await websocket.accept()

    # Send connection confirmation
    await websocket.send_json({
        "type": "connection",
        "status": "connected",
        "project_id": project_id,
        "message": "🚀 Connected to fantastic reasoning AI!"
    })

    try:
        while True:
            data = await websocket.receive_text()

            # Send acknowledgment
            await websocket.send_json({
                "type": "message_received",
                "status": "processing",
                "message": "🧠 Processing your request..."
            })

            # Get the base agent and use streaming reply
            base_agent = project.agent_manager.get_agent("base")
            if base_agent:
                try:
                    # The agent's reply method now handles all websocket communication,
                    # including sending the final message.
                    base_agent.reply(data, websocket=websocket, max_tool_calls=15)
                except Exception as e:
                    await websocket.send_json({
                        "type": "error",
                        "message": f"Processing error: {str(e)}"
                    })
            else:
                await websocket.send_json({
                    "type": "error",
                    "message": "Agent not available"
                })

    except WebSocketDisconnect:
        pass  # ProjectManager will be cleaned up automatically

@router.post("/project/{project_id}/upload")
async def upload_input_file(project_id: str, file: UploadFile = File(...)):
    print(f"Uploading file {file.filename} to project {project_id}")
    try:
        # Get the project using existing pattern
        project = ProjectManager.from_project_id(project_id)
        if not project:
            raise HTTPException(status_code=404, detail="Project not found")

        # Read file content
        content = await file.read()

        # Save file to input folder (existing functionality)
        saved_path = project.save_file(file.filename, content, location="input")

        # Smart file processing based on type
        processing_result = None
        file_ext = file.filename.lower().split('.')[-1] if '.' in file.filename else ''

        if file_ext == 'csv':
            # Smart CSV handling - analyze and create metadata, but DON'T RAG the content
            try:
                # Analyze CSV from saved file
                input_csv_path = saved_path
                metadata = analyze_csv(input_csv_path)

                # Save metadata
                metadata_file = save_csv_metadata(metadata, project_id, file.filename)

                # Create project structure summary for RAG (so model knows about files)
                project_structure_info = f"""
Project File: {file.filename} (CSV Dataset)
Location: input/{file.filename}
Type: CSV Data File
Rows: {metadata['file_info']['total_rows']:,}
Columns: {metadata['file_info']['total_columns']}
Size: {metadata['file_info']['file_size_mb']} MB

Column Structure:
{', '.join(metadata['summary'].get('numeric_columns', []))} (numeric)
{', '.join(metadata['summary'].get('categorical_columns', []))} (categorical)
{', '.join(metadata['summary'].get('datetime_columns', []))} (datetime)

Usage: Load with pd.read_csv("input/{file.filename}")
Analysis: Use code_execution tool to analyze this dataset
"""

                # Generate analysis code template
                analysis_code = generate_csv_analysis_code(metadata, file.filename)

                # Save analysis code to work directory
                work_dir = f"projects/{project_id}/work"
                os.makedirs(work_dir, exist_ok=True)
                with open(f"{work_dir}/analyze_{file.filename.replace('.csv', '')}.py", 'w') as f:
                    f.write(analysis_code)

                # RAG only the project structure info (NOT the CSV content)
                base_agent = project.agent_manager.get_agent("base")
                if base_agent and base_agent.document_manager:
                    rag_result = base_agent.document_manager.process_text_content(
                        project_structure_info,
                        f"project_file_{file.filename}",
                        {"project_id": project_id, "file_type": "csv_file_info", "file_location": f"input/{file.filename}"}
                    )

                processing_result = {
                    "status": "success",
                    "file_type": "csv",
                    "location": f"input/{file.filename}",
                    "metadata_file": metadata_file,
                    "analysis_code_generated": True,
                    "structure_indexed": True,
                    "summary": metadata.get("summary", {})
                }

            except Exception as e:
                processing_result = {"status": "error", "error": f"CSV processing failed: {str(e)}"}

        else:
            # Regular file processing through RAG
            try:
                base_agent = project.agent_manager.get_agent("base")
                if base_agent and base_agent.document_manager:
                    result = base_agent.document_manager.process_file_upload(
                        content,
                        file.filename,
                        {"project_id": project_id}
                    )
                    processing_result = result
            except Exception as e:
                processing_result = {"status": "error", "error": str(e)}

        response = {
            "filename": file.filename,
            "status": "uploaded to input folder",
            "saved_path": saved_path
        }

        if processing_result:
            response["processing"] = processing_result

        return response

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"File upload failed: {str(e)}")

@router.post("/project/{project_id}/search")
async def search_project(project_id: str, query: str):
    """Simple search endpoint for Graph-RAG queries."""
    try:
        project = ProjectManager.from_project_id(project_id)
        if not project:
            raise HTTPException(status_code=404, detail="Project not found")

        # Use the agent's reply method which will automatically use Graph-RAG
        response = project.agent_manager.generate_response(query)

        return {
            "query": query,
            "response": response,
            "project_id": project_id
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")
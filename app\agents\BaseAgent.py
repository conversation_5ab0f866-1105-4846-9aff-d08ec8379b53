from typing import Dict
import json
import os
import subprocess
import requests
from bs4 import BeautifulSoup
from ..managers.DocumentManager import DocumentManager
from ..providers.llm import get_llm_provider
from ..providers.embeddings import get_embedding_provider

class BaseAgent:
    """Simple, powerful AI agent with Graph-RAG, web search, and code execution capabilities."""

    access_specifier: str = "General"
    model_name: str = "gpt-4o-mini"

    def __init__(self, project_id: str = None, model_name: str = None, access_specifier: str = None, db_config: Dict = None):
        self.project_id = project_id
        self.model_name = model_name or self.model_name
        self.access_specifier = access_specifier or self.access_specifier

        # Initialize components safely
        self.document_manager = None
        self.llm_provider = None

        # Try to initialize document manager if project_id provided
        if project_id:
            try:
                self.document_manager = DocumentManager(project_id=project_id, db_config=db_config)
            except Exception as e:
                print(f"Warning: Could not initialize DocumentManager: {e}")

        # Try to initialize LLM provider
        try:
            self.llm_provider = get_llm_provider(model=self.model_name)
            
        
        except Exception as e:
            print(f"Warning: Could not initialize LLM provider: {e}")

        # Create work directory
        if project_id:
            self.work_dir = f"projects/{project_id}/work"
            os.makedirs(self.work_dir, exist_ok=True)
        else:
            self.work_dir = "work"
            os.makedirs(self.work_dir, exist_ok=True)

        # Define tools
        self.tools = [
            {
                "type": "function",
                "function": {
                    "name": "project_search",
                    "description": "Search through project documents for relevant information",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "query": {"type": "string", "description": "Search query"},
                            "k": {"type": "integer", "description": "Number of results (default: 5)"}
                        },
                        "required": ["query"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "web_search",
                    "description": "Search the web and scrape content from URLs",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "query": {"type": "string", "description": "Search query or URL to scrape"},
                            "is_url": {"type": "boolean", "description": "True if query is a URL to scrape, False for search"}
                        },
                        "required": ["query"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "code_execution",
                    "description": "Write and execute Python code for data analysis",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "code": {"type": "string", "description": "Python code to execute"},
                            "filename": {"type": "string", "description": "Optional filename to save code"}
                        },
                        "required": ["code"]
                    }
                }
            }
        ]

    def _build_system_prompt(self) -> str:
        project_info = ""
        if self.project_id:
            project_info = f"""

PROJECT STRUCTURE:
- input/ : Uploaded files (CSV, documents, etc.)
- output/ : Analysis results and generated files
- work/ : Your code execution workspace
- metadata/ : File metadata and analysis info

When working with CSV files, use: pd.read_csv("input/filename.csv")
When saving results, use: "output/filename"
All code runs in the work/ directory.
"""

        return f"""You are a powerful AI assistant with access to:
- project_search: Search through uploaded project documents and file information
- web_search: Search the web or scrape specific URLs
- code_execution: Write and run Python code for data analysis

{project_info}
Always search for context before answering. Use tools to provide accurate, helpful responses.
Model: {self.model_name} | Project: {self.project_id or 'General'}"""

    def project_search(self, query: str, k: int = 5) -> str:
        """Search through project documents."""
        if not self.document_manager:
            return json.dumps({"error": "No project loaded"})

        try:
            results = self.document_manager.vector_search(query, k)
            return json.dumps({
                "found": len(results),
                "query": query,
                "results": [{"content": r["content"], "score": r["score"], "metadata": r.get("metadata", {})} for r in results]
            })
        except Exception as e:
            return json.dumps({"error": str(e)})

    def web_search(self, query: str, is_url: bool = False) -> str:
        """Search web or scrape URL."""
        try:
            if is_url:
                # Scrape specific URL
                response = requests.get(query, timeout=10)
                soup = BeautifulSoup(response.content, 'html.parser')

                # Extract text content
                for script in soup(["script", "style"]):
                    script.decompose()
                text = soup.get_text()
                lines = (line.strip() for line in text.splitlines())
                text = '\n'.join(line for line in lines if line)

                return json.dumps({
                    "url": query,
                    "title": soup.title.string if soup.title else "No title",
                    "content": text[:2000]  # Limit content
                })
            else:
                # Simple web search using DuckDuckGo (no API key needed)
                search_url = f"https://duckduckgo.com/html/?q={query}"
                headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
                response = requests.get(search_url, headers=headers, timeout=10)
                soup = BeautifulSoup(response.content, 'html.parser')

                results = []
                for result in soup.find_all('div', class_='result')[:5]:
                    title_elem = result.find('a', class_='result__a')
                    snippet_elem = result.find('a', class_='result__snippet')

                    if title_elem:
                        results.append({
                            "title": title_elem.get_text().strip(),
                            "url": title_elem.get('href', ''),
                            "snippet": snippet_elem.get_text().strip() if snippet_elem else ""
                        })

                return json.dumps({
                    "query": query,
                    "results": results
                })
        except Exception as e:
            return json.dumps({"error": str(e)})

    def code_execution(self, code: str, filename: str = None) -> str:
        """Execute Python code in work directory."""
        try:
            # Save code to file if filename provided
            if filename:
                filepath = os.path.join(self.work_dir, filename)
                with open(filepath, 'w') as f:
                    f.write(code)

            # Execute code and capture output
            result = subprocess.run(
                ['python', '-c', code],
                cwd=self.work_dir,
                capture_output=True,
                text=True,
                timeout=30
            )

            return json.dumps({
                "code": code,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "returncode": result.returncode,
                "saved_to": filename if filename else None
            })
        except subprocess.TimeoutExpired:
            return json.dumps({"error": "Code execution timed out"})
        except Exception as e:
            return json.dumps({"error": str(e)})

    def reply(self, message: str, context: Dict = None) -> str:
        """Main method to handle user messages with tool support."""
        if not self.llm_provider:
            return "Error: LLM provider not initialized. Please check your API keys."

        try:
            system_prompt = self._build_system_prompt()
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": message}
            ]

            response = self.llm_provider.chat(
                messages=messages,
                tools=self.tools,
                tool_choice="auto",
                max_tokens=1000,
                temperature=0.7
            )

            # Handle tool calls if any (OpenAI format)
            if response.get("tool_calls"):
                return self._handle_tool_calls(message, response)
            else:
                return response.get("content", "No response generated")

        except Exception as e:
            return f"Error: {str(e)}"

    def _handle_tool_calls(self, original_message: str, response_data: Dict) -> str:
        """Handle tool calls and get final response."""
        # Simplified tool handling - execute tools and return results
        tool_calls = response_data.get("tool_calls", [])
        if not tool_calls:
            return response_data.get("content", "No response")

        results = []
        for tool_call in tool_calls:
            function_name = tool_call.function.name
            function_args = json.loads(tool_call.function.arguments)

            # Call the appropriate tool method
            if function_name == "project_search":
                result = self.project_search(function_args.get("query", ""), function_args.get("k", 5))
            elif function_name == "web_search":
                result = self.web_search(function_args.get("query", ""), function_args.get("is_url", False))
            elif function_name == "code_execution":
                result = self.code_execution(function_args.get("code", ""), function_args.get("filename"))
            else:
                result = json.dumps({"error": f"Unknown function: {function_name}"})

            results.append(f"🔧 {function_name}: {result}")

        # Return tool results directly for now (simplified)
        return "\n".join(results)
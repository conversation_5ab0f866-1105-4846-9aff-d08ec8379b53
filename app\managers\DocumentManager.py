import os
import psycopg2
from typing import List, Dict, Optional, Tuple
import uuid
from datetime import datetime

# Simple Graph-RAG imports
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_postgres import PGVector
from langchain.schema import Document
from ..providers.embeddings import get_embedding_provider


class DocumentManager:
    """Simple document manager for Graph-RAG functionality."""

    def __init__(self, project_id: str = None, db_config: Dict = None):
        self.project_id = project_id

        # Default PostgreSQL configuration
        self.db_config = db_config or {
            "host": os.getenv("POSTGRES_HOST", "localhost"),
            "port": os.getenv("POSTGRES_PORT", "5432"),
            "database": os.getenv("POSTGRES_DB", "kairos_graphrag"),
            "user": os.getenv("POSTGRES_USER", "postgres"),
            "password": os.getenv("POSTGRES_PASSWORD", "password")
        }

        # Initialize components
        try:
            self.embedding_provider = get_embedding_provider()
            print(f"✅ Initialized embedding provider: {os.getenv('EMBEDDING_PROVIDER', 'openai')}")
        except Exception as e:
            print(f"Warning: Could not initialize embedding provider: {e}")
            self.embedding_provider = None

        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=500,
            chunk_overlap=100,
        )

        # Connection string for pgvector (new langchain_postgres uses psycopg3 format)
        self.connection_string = f"postgresql://{self.db_config['user']}:{self.db_config['password']}@{self.db_config['host']}:{self.db_config['port']}/{self.db_config['database']}"

        # Initialize database tables first
        self._init_db()

        # Initialize vector store with new langchain_postgres
        collection_name = f"documents_{self.project_id}".replace("-", "_") if self.project_id else "documents"

        try:
            # Create the vector store - it will create tables automatically
            self.vector_store = PGVector(
                embeddings=self.embedding_provider,
                collection_name=collection_name,
                connection=self.connection_string,
                use_jsonb=True,  # Use JSONB for metadata
                pre_delete_collection=False,  # Don't delete existing data
                logger=None  # Disable logging
            )
        except Exception as e:
            print(f"Vector store initialization failed: {e}")
            self.vector_store = None
    
    def _sanitize_table_name(self, base_name: str, project_id: str = None) -> str:
        """
        Sanitize table name by replacing invalid characters.
        PostgreSQL table names cannot contain hyphens.
        
        Args:
            base_name: Base table name (e.g., 'triples', 'documents')
            project_id: Optional project ID to append
            
        Returns:
            Sanitized table name safe for PostgreSQL
        """
        if project_id:
            # Replace hyphens and other invalid chars with underscores
            sanitized_id = project_id.replace("-", "_").replace(" ", "_")
            return f"{base_name}_{sanitized_id}"
        return base_name
    
    def _get_triples_table_name(self) -> str:
        """Get the sanitized triples table name for this project."""
        return self._sanitize_table_name("triples", self.project_id)
    


    def _init_db(self):
        """Initialize PostgreSQL database with pgvector extension."""
        try:
            conn = psycopg2.connect(**self.db_config)
            cur = conn.cursor()

            # Create pgvector extension
            cur.execute("CREATE EXTENSION IF NOT EXISTS vector;")

            # DON'T drop tables - let them persist!
            # The langchain_postgres will create tables if they don't exist

            conn.commit()
            cur.close()
            conn.close()

        except Exception as e:
            print(f"Database initialization error: {e}")
    
    def process_text_content(self, content: str, filename: str, metadata: Dict = None) -> Dict:
        """Process text content through the Graph-RAG pipeline."""
        try:
            if not self.vector_store:
                return {
                    "status": "error",
                    "error": "Vector store not initialized"
                }

            # Chunk the document
            chunks = self.text_splitter.split_text(content)

            # Create documents with metadata
            docs = []
            chunk_ids = []
            for i, chunk in enumerate(chunks):
                chunk_id = str(uuid.uuid4())
                chunk_ids.append(chunk_id)

                doc_metadata = {
                    "chunk_id": chunk_id,
                    "chunk_index": i,
                    "source_file": filename,
                    "project_id": self.project_id,
                    **(metadata or {})
                }
                docs.append(Document(page_content=chunk, metadata=doc_metadata))

            # Store embeddings in vector database
            self.vector_store.add_documents(docs)

            return {
                "status": "success",
                "chunks_processed": len(chunks),
                "chunk_ids": chunk_ids
            }

        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    

    
    def vector_search(self, query: str, k: int = 5) -> List[Dict]:
        """Perform vector similarity search."""
        try:
            if not self.vector_store:
                return []
            results = self.vector_store.similarity_search_with_score(query, k=k)
            return [
                {
                    "content": doc.page_content,
                    "metadata": doc.metadata,
                    "score": score
                }
                for doc, score in results
            ]
        except Exception as e:
            print(f"Vector search error: {e}")
            return []

    def hybrid_search(self, query: str, k: int = 5) -> Dict:
        """Perform vector search only (simplified)."""
        vector_results = self.vector_search(query, k)

        return {
            "vector_results": vector_results,
            "knowledge_graph_results": []  # Simplified - no KG for now
        }
    
    def process_file_upload(self, file_content: bytes, filename: str, metadata: Dict = None) -> Dict:
        """Simple file processing - just handle text content."""
        try:
            # Try to decode as text
            if isinstance(file_content, bytes):
                content = file_content.decode('utf-8')
            else:
                content = str(file_content)

            # Process the text content
            return self.process_text_content(content, filename, metadata)

        except Exception as e:
            return {
                "status": "error",
                "error": f"File processing failed: {str(e)}"
            }

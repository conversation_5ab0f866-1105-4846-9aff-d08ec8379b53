#!/usr/bin/env python3
"""
Quick test to see if the model continues talking after tool execution.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_model_conversation():
    """Test if model continues conversation after tools."""
    print("🗣️ Testing Model Conversation Flow")
    print("=" * 50)
    
    try:
        from app.agents.BaseAgent import BaseAgent
        
        # Create agent
        agent = BaseAgent(project_id="4191a198-b6df-4a5c-afaf-89d0c11b47e1")
        print(f"✅ Agent initialized with {agent.llm_provider.__class__.__name__}")
        
        # Test simple question that should trigger tools
        print("\n🧠 Testing reasoning with simple question...")
        response = agent.reply(
            "What is IVF? Search for information about it.",
            max_tool_calls=5
        )
        
        print(f"\n📊 Response length: {len(response)} characters")
        print(f"Response preview:\n{response[:500]}...")
        
        if len(response) > 100:
            print("✅ Model is talking and providing responses!")
        else:
            print("❌ Model response too short - might not be following up")
            
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_direct_llm():
    """Test LLM directly to see if it's working."""
    print("\n🤖 Testing LLM Directly")
    print("=" * 30)
    
    try:
        from app.providers.llm import get_llm_provider
        
        llm = get_llm_provider()
        print(f"✅ LLM provider: {type(llm).__name__}")
        
        # Simple test
        response = llm.chat([
            {"role": "user", "content": "Say hello and explain what you are in one sentence."}
        ])
        
        content = response.get("content", "No content")
        print(f"✅ LLM response: {content}")
        
        return True
        
    except Exception as e:
        print(f"❌ LLM test failed: {e}")
        return False

def main():
    """Run tests."""
    print("🔍 Debugging Model Communication")
    print("=" * 60)
    
    # Test LLM first
    llm_ok = test_direct_llm()
    
    if llm_ok:
        # Test full conversation
        conv_ok = test_model_conversation()
        
        if conv_ok:
            print("\n🎉 Model should be talking now!")
        else:
            print("\n❌ Model conversation issues")
    else:
        print("\n❌ LLM provider issues")

if __name__ == "__main__":
    main()

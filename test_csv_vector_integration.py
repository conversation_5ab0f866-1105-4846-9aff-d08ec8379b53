#!/usr/bin/env python3
"""
Test CSV vector database integration.
"""

import os
import pandas as pd
from pathlib import Path
from dotenv import load_dotenv

# Load environment
load_dotenv()

def test_csv_vector_integration():
    print('🔗 Testing CSV Vector Database Integration')
    print('=' * 50)
    
    try:
        from app.utils.csv_vector_integration import add_csv_to_vector_db, search_csv_metadata
        
        # Create test CSV
        test_data = {
            'product_name': ['iPhone', 'Samsung Galaxy', 'Google Pixel'],
            'price': [999, 899, 799],
            'category': ['Electronics', 'Electronics', 'Electronics'],
            'rating': [4.5, 4.3, 4.2]
        }
        df = pd.DataFrame(test_data)
        test_file = 'test_products.csv'
        df.to_csv(test_file, index=False)
        
        project_id = "test-csv-vector"
        
        print(f"📊 Adding CSV to vector database...")
        result = add_csv_to_vector_db(test_file, project_id)
        
        if result["success"]:
            print(f"✅ {result['message']}")
            print(f"   Vector ID: {result.get('vector_id', 'N/A')}")
            print(f"   Metadata file: {result.get('metadata_file', 'N/A')}")
            
            # Test search
            print(f"\n🔍 Testing vector search...")
            search_results = search_csv_metadata("products electronics price", project_id, k=3)
            
            if search_results:
                print(f"✅ Found {len(search_results)} results")
                for i, result in enumerate(search_results):
                    print(f"   Result {i+1}: {result['filename']} ({result['total_rows']} rows)")
            else:
                print("❌ No search results found")
                
        else:
            print(f"❌ Failed to add CSV: {result['error']}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Cleanup
        if os.path.exists(test_file):
            os.remove(test_file)
            print(f"\n🧹 Cleaned up: {test_file}")

def main():
    success = test_csv_vector_integration()
    
    if success:
        print('\n🎉 CSV Vector Integration Working!')
        print('✅ CSV metadata extraction: Simple and robust')
        print('✅ Vector database indexing: Automatic')
        print('✅ Search functionality: Working')
        print('✅ File location tracking: Included')
        print('\nNow you can:')
        print('- Upload CSV files to input folder')
        print('- Metadata automatically extracted and indexed')
        print('- Search for CSV files by content/structure')
        print('- Get file locations for code execution')
    else:
        print('\n❌ Integration needs fixes')

if __name__ == "__main__":
    main()

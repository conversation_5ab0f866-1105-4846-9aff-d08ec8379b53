#!/usr/bin/env python3
"""
Test the simplified CSV handler.
"""

from app.utils.csv_handler import analyze_csv, process_csv_for_project
import pandas as pd
import os

def test_csv_handler():
    print('🧪 Testing Simplified CSV Handler')
    print('=' * 40)

    # Create a simple test CSV
    test_data = {
        'name': ['<PERSON>', '<PERSON>', '<PERSON>', None],
        'age': [25, 30, 35, 40],
        'city': ['NYC', 'LA', 'Chicago', 'Boston'],
        'salary': [50000, 60000, None, 80000]
    }
    df = pd.DataFrame(test_data)
    test_file = 'test_simple.csv'
    df.to_csv(test_file, index=False)

    try:
        # Test analysis
        print("📊 Testing CSV analysis...")
        metadata = analyze_csv(test_file)
        
        if "error" in metadata:
            print(f"❌ Analysis failed: {metadata['error']}")
            return False
        
        filename = metadata['file_info']['filename']
        rows = metadata['file_info']['total_rows']
        cols = metadata['file_info']['total_columns']
        
        print(f'✅ Analysis successful: {filename}')
        print(f'   Rows: {rows}, Columns: {cols}')
        print(f'   Columns: {", ".join(metadata["columns"])}')
        
        # Test processing
        print("\n🔄 Testing CSV processing...")
        result = process_csv_for_project(test_file, 'test-project')
        
        if result.get('success'):
            print('✅ Processing successful')
            print(f'   Summary: {result["summary"]}')
            print(f'   Vector content length: {len(result["vector_content"])} chars')
            print(f'   Metadata file: {result["metadata_file"]}')
            
            # Show sample of vector content
            vector_content = result["vector_content"]
            print(f'\n📝 Vector content preview:')
            print(vector_content[:300] + "..." if len(vector_content) > 300 else vector_content)
            
        else:
            error_msg = result.get('error', 'Unknown error')
            print(f'❌ Processing failed: {error_msg}')
            return False
        
        return True
        
    except Exception as e:
        print(f'❌ Test failed: {e}')
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Cleanup
        if os.path.exists(test_file):
            os.remove(test_file)
            print(f'\n🧹 Cleaned up test file: {test_file}')

def main():
    success = test_csv_handler()
    
    if success:
        print('\n🎉 CSV handler is working perfectly!')
        print('✅ No more division by zero errors')
        print('✅ Simple, robust metadata extraction')
        print('✅ Ready for vector database indexing')
    else:
        print('\n❌ CSV handler needs fixes')

if __name__ == "__main__":
    main()

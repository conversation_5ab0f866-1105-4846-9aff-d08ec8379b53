import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np

# Load the CSV file
df = pd.read_csv("input/sample_data.csv")

print("=== CSV ANALYSIS REPORT ===")
print(f"Dataset shape: {df.shape}")
print(f"Memory usage: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB")

# Basic info
print("\n=== BASIC INFO ===")
print(df.info())

# Missing data analysis
print("\n=== MISSING DATA ===")
missing = df.isnull().sum()
missing_pct = (missing / len(df)) * 100
missing_df = pd.DataFrame({'Missing Count': missing, 'Missing %': missing_pct})
print(missing_df[missing_df['Missing Count'] > 0])

# Numeric columns analysis
numeric_cols = ['customer_id', 'age', 'income', 'purchase_amount', 'satisfaction']
if numeric_cols:
    print("\n=== NUMERIC COLUMNS ANALYSIS ===")
    print(df[numeric_cols].describe())

    # Correlation matrix
    if len(numeric_cols) > 1:
        plt.figure(figsize=(10, 8))
        sns.heatmap(df[numeric_cols].corr(), annot=True, cmap='coolwarm', center=0)
        plt.title('Correlation Matrix')
        plt.tight_layout()
        plt.savefig('output/correlation_matrix.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("Correlation matrix saved to output/correlation_matrix.png")

# Categorical columns analysis
categorical_cols = ['category']
if categorical_cols:
    print("\n=== CATEGORICAL COLUMNS ANALYSIS ===")
    for col in categorical_cols[:3]:  # Limit to first 3 categorical columns
        print(f"\n{col} - Top 10 values:")
        print(df[col].value_counts().head(10))

# Data quality checks
print("\n=== DATA QUALITY CHECKS ===")
print(f"Duplicate rows: {df.duplicated().sum()}")
print(f"Columns with all null values: {df.columns[df.isnull().all()].tolist()}")
print(f"Columns with single unique value: {df.columns[df.nunique() == 1].tolist()}")

# Save summary to file
summary = {
    "total_rows": len(df),
    "total_columns": len(df.columns),
    "numeric_columns": len(numeric_cols),
    "categorical_columns": len(categorical_cols),
    "missing_data_percentage": (df.isnull().sum().sum() / (len(df) * len(df.columns))) * 100,
    "duplicate_rows": df.duplicated().sum()
}

with open('output/analysis_summary.json', 'w') as f:
    import json
    json.dump(summary, f, indent=2)

print("\nAnalysis complete! Check output/ folder for results.")
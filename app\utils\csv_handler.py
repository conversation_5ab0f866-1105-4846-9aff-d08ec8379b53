"""Smart CSV file handling - generate metadata instead of RAG-ing entire content."""

import pandas as pd
import json
import os
from typing import Dict, Any, List
from pathlib import Path

def analyze_csv(file_path: str, sample_rows: int = 100) -> Dict[str, Any]:
    """Analyze CSV and generate comprehensive metadata."""
    try:
        # Read sample for analysis
        df_sample = pd.read_csv(file_path, nrows=sample_rows)
        df_full = pd.read_csv(file_path)
        
        # Basic info
        metadata = {
            "file_info": {
                "filename": Path(file_path).name,
                "total_rows": len(df_full),
                "total_columns": len(df_full.columns),
                "file_size_mb": round(os.path.getsize(file_path) / (1024*1024), 2)
            },
            "columns": {},
            "summary": {},
            "sample_data": df_sample.head(5).to_dict('records'),
            "data_types": df_full.dtypes.astype(str).to_dict()
        }
        
        # Analyze each column
        for col in df_full.columns:
            col_data = df_full[col]
            col_info = {
                "type": str(col_data.dtype),
                "null_count": int(col_data.isnull().sum()),
                "null_percentage": round(col_data.isnull().sum() / len(col_data) * 100, 2),
                "unique_count": int(col_data.nunique())
            }
            
            # Numeric columns
            if pd.api.types.is_numeric_dtype(col_data):
                col_info.update({
                    "min": float(col_data.min()) if not col_data.empty else None,
                    "max": float(col_data.max()) if not col_data.empty else None,
                    "mean": float(col_data.mean()) if not col_data.empty else None,
                    "std": float(col_data.std()) if not col_data.empty else None,
                    "quartiles": col_data.quantile([0.25, 0.5, 0.75]).to_dict()
                })
            
            # Categorical/text columns
            elif pd.api.types.is_object_dtype(col_data):
                value_counts = col_data.value_counts().head(10)
                col_info.update({
                    "top_values": value_counts.to_dict(),
                    "avg_length": float(col_data.astype(str).str.len().mean()) if not col_data.empty else None
                })
            
            # DateTime columns
            elif pd.api.types.is_datetime64_any_dtype(col_data):
                col_info.update({
                    "min_date": str(col_data.min()) if not col_data.empty else None,
                    "max_date": str(col_data.max()) if not col_data.empty else None,
                    "date_range_days": (col_data.max() - col_data.min()).days if not col_data.empty else None
                })
            
            metadata["columns"][col] = col_info
        
        # Generate summary insights
        metadata["summary"] = {
            "description": f"Dataset with {len(df_full)} rows and {len(df_full.columns)} columns",
            "numeric_columns": [col for col in df_full.columns if pd.api.types.is_numeric_dtype(df_full[col])],
            "categorical_columns": [col for col in df_full.columns if pd.api.types.is_object_dtype(df_full[col])],
            "datetime_columns": [col for col in df_full.columns if pd.api.types.is_datetime64_any_dtype(df_full[col])],
            "missing_data_columns": [col for col in df_full.columns if df_full[col].isnull().sum() > 0],
            "potential_id_columns": [col for col in df_full.columns if df_full[col].nunique() == len(df_full)],
            "high_cardinality_columns": [col for col in df_full.columns if df_full[col].nunique() > len(df_full) * 0.8]
        }
        
        return metadata
        
    except Exception as e:
        return {
            "error": f"Failed to analyze CSV: {str(e)}",
            "file_info": {"filename": Path(file_path).name}
        }

def generate_csv_analysis_code(metadata: Dict[str, Any], filename: str) -> str:
    """Generate Python code for CSV analysis based on metadata."""
    
    code = f'''
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np

# Load the CSV file
df = pd.read_csv("input/{filename}")

print("=== CSV ANALYSIS REPORT ===")
print(f"Dataset shape: {{df.shape}}")
print(f"Memory usage: {{df.memory_usage(deep=True).sum() / 1024**2:.2f}} MB")

# Basic info
print("\\n=== BASIC INFO ===")
print(df.info())

# Missing data analysis
print("\\n=== MISSING DATA ===")
missing = df.isnull().sum()
missing_pct = (missing / len(df)) * 100
missing_df = pd.DataFrame({{'Missing Count': missing, 'Missing %': missing_pct}})
print(missing_df[missing_df['Missing Count'] > 0])

# Numeric columns analysis
numeric_cols = {repr(metadata.get("summary", {}).get("numeric_columns", []))}
if numeric_cols:
    print("\\n=== NUMERIC COLUMNS ANALYSIS ===")
    print(df[numeric_cols].describe())

    # Correlation matrix
    if len(numeric_cols) > 1:
        plt.figure(figsize=(10, 8))
        sns.heatmap(df[numeric_cols].corr(), annot=True, cmap='coolwarm', center=0)
        plt.title('Correlation Matrix')
        plt.tight_layout()
        plt.savefig('output/correlation_matrix.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("Correlation matrix saved to output/correlation_matrix.png")

# Categorical columns analysis
categorical_cols = {repr(metadata.get("summary", {}).get("categorical_columns", []))}
if categorical_cols:
    print("\\n=== CATEGORICAL COLUMNS ANALYSIS ===")
    for col in categorical_cols[:3]:  # Limit to first 3 categorical columns
        print(f"\\n{{col}} - Top 10 values:")
        print(df[col].value_counts().head(10))

# Data quality checks
print("\\n=== DATA QUALITY CHECKS ===")
print(f"Duplicate rows: {{df.duplicated().sum()}}")
print(f"Columns with all null values: {{df.columns[df.isnull().all()].tolist()}}")
print(f"Columns with single unique value: {{df.columns[df.nunique() == 1].tolist()}}")

# Save summary to file
summary = {{
    "total_rows": len(df),
    "total_columns": len(df.columns),
    "numeric_columns": len(numeric_cols),
    "categorical_columns": len(categorical_cols),
    "missing_data_percentage": (df.isnull().sum().sum() / (len(df) * len(df.columns))) * 100,
    "duplicate_rows": df.duplicated().sum()
}}

with open('output/analysis_summary.json', 'w') as f:
    import json
    json.dump(summary, f, indent=2)

print("\\nAnalysis complete! Check output/ folder for results.")
'''
    
    return code.strip()

def save_csv_metadata(metadata: Dict[str, Any], project_id: str, filename: str) -> str:
    """Save CSV metadata to project folder."""
    metadata_dir = f"projects/{project_id}/metadata"
    os.makedirs(metadata_dir, exist_ok=True)
    
    metadata_file = f"{metadata_dir}/{filename}_metadata.json"
    with open(metadata_file, 'w') as f:
        json.dump(metadata, f, indent=2)
    
    return metadata_file

def create_csv_summary_for_rag(metadata: Dict[str, Any]) -> str:
    """Create a concise summary suitable for RAG."""
    summary = metadata.get("summary", {})
    file_info = metadata.get("file_info", {})
    
    text_summary = f"""
CSV Dataset: {file_info.get('filename', 'Unknown')}
Rows: {file_info.get('total_rows', 0):,} | Columns: {file_info.get('total_columns', 0)} | Size: {file_info.get('file_size_mb', 0)}MB

Column Types:
- Numeric: {', '.join(summary.get('numeric_columns', []))}
- Categorical: {', '.join(summary.get('categorical_columns', []))}
- DateTime: {', '.join(summary.get('datetime_columns', []))}

Data Quality:
- Missing data in: {', '.join(summary.get('missing_data_columns', []))}
- Potential ID columns: {', '.join(summary.get('potential_id_columns', []))}

Key Insights: This dataset contains {file_info.get('total_rows', 0):,} records with {len(summary.get('numeric_columns', []))} numeric and {len(summary.get('categorical_columns', []))} categorical variables.
"""
    
    return text_summary.strip()

"""Smart CSV file handling - generate metadata instead of RAG-ing entire content."""

import pandas as pd
import json
import os
from typing import Dict, Any, List
from pathlib import Path

def analyze_csv(file_path: str, sample_rows: int = 100) -> Dict[str, Any]:
    """Simple, robust CSV analysis with metadata for vector search."""
    try:
        # Read CSV safely
        df = pd.read_csv(file_path, nrows=sample_rows)

        if df.empty:
            return {
                "error": "CSV file is empty",
                "file_path": file_path,
                "filename": Path(file_path).name
            }

        # Get file info
        file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0

        # Simple metadata
        metadata = {
            "file_info": {
                "filename": Path(file_path).name,
                "file_path": file_path,
                "total_rows": len(df),
                "total_columns": len(df.columns),
                "file_size_mb": round(file_size / (1024*1024), 2) if file_size > 0 else 0
            },
            "columns": {},  # Changed to dict to store column details
            "column_names": list(df.columns),  # Keep list of column names
            "column_types": {col: str(df[col].dtype) for col in df.columns},
            "sample_data": df.head(3).to_dict('records') if len(df) > 0 else [],
            "summary": f"CSV file '{Path(file_path).name}' with {len(df)} rows and {len(df.columns)} columns"
        }

        # Simple column analysis (avoid division by zero)
        for col in df.columns:
            col_data = df[col]
            total_rows = len(col_data)
            null_count = col_data.isnull().sum()

            col_info = {
                "type": str(col_data.dtype),
                "null_count": int(null_count),
                "null_percentage": round((null_count / total_rows * 100), 2) if total_rows > 0 else 0,
                "unique_count": int(col_data.nunique())
            }
            
            # Add simple stats for numeric columns
            if pd.api.types.is_numeric_dtype(col_data) and not col_data.empty:
                try:
                    col_info["min"] = float(col_data.min())
                    col_info["max"] = float(col_data.max())
                    col_info["mean"] = float(col_data.mean())
                except:
                    pass

            # Add top values for categorical columns
            elif pd.api.types.is_object_dtype(col_data) and not col_data.empty:
                try:
                    top_values = col_data.value_counts().head(5).to_dict()
                    col_info["top_values"] = {str(k): int(v) for k, v in top_values.items()}
                except:
                    pass

            metadata["columns"][col] = col_info

        # Create searchable description for vector DB
        description_parts = [
            f"CSV file: {Path(file_path).name}",
            f"Location: {file_path}",
            f"Contains {len(df)} rows and {len(df.columns)} columns",
            f"Columns: {', '.join(df.columns[:10])}{'...' if len(df.columns) > 10 else ''}",
        ]

        # Add column type info
        numeric_cols = [col for col in df.columns if pd.api.types.is_numeric_dtype(df[col])]
        text_cols = [col for col in df.columns if pd.api.types.is_object_dtype(df[col])]

        if numeric_cols:
            description_parts.append(f"Numeric columns: {', '.join(numeric_cols[:5])}")
        if text_cols:
            description_parts.append(f"Text columns: {', '.join(text_cols[:5])}")

        metadata["searchable_description"] = ". ".join(description_parts)
        
        return metadata
        
    except Exception as e:
        return {
            "error": f"Failed to analyze CSV: {str(e)}",
            "file_info": {"filename": Path(file_path).name}
        }

def generate_csv_analysis_code(metadata: Dict[str, Any], filename: str) -> str:
    """Generate Python code for CSV analysis based on metadata."""
    
    code = f'''
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np

# Load the CSV file
df = pd.read_csv("input/{filename}")

print("=== CSV ANALYSIS REPORT ===")
print(f"Dataset shape: {{df.shape}}")
print(f"Memory usage: {{df.memory_usage(deep=True).sum() / 1024**2:.2f}} MB")

# Basic info
print("\\n=== BASIC INFO ===")
print(df.info())

# Missing data analysis
print("\\n=== MISSING DATA ===")
missing = df.isnull().sum()
missing_pct = (missing / len(df)) * 100
missing_df = pd.DataFrame({{'Missing Count': missing, 'Missing %': missing_pct}})
print(missing_df[missing_df['Missing Count'] > 0])

# Numeric columns analysis
numeric_cols = {repr(metadata.get("summary", {}).get("numeric_columns", []))}
if numeric_cols:
    print("\\n=== NUMERIC COLUMNS ANALYSIS ===")
    print(df[numeric_cols].describe())

    # Correlation matrix
    if len(numeric_cols) > 1:
        plt.figure(figsize=(10, 8))
        sns.heatmap(df[numeric_cols].corr(), annot=True, cmap='coolwarm', center=0)
        plt.title('Correlation Matrix')
        plt.tight_layout()
        plt.savefig('output/correlation_matrix.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("Correlation matrix saved to output/correlation_matrix.png")

# Categorical columns analysis
categorical_cols = {repr(metadata.get("summary", {}).get("categorical_columns", []))}
if categorical_cols:
    print("\\n=== CATEGORICAL COLUMNS ANALYSIS ===")
    for col in categorical_cols[:3]:  # Limit to first 3 categorical columns
        print(f"\\n{{col}} - Top 10 values:")
        print(df[col].value_counts().head(10))

# Data quality checks
print("\\n=== DATA QUALITY CHECKS ===")
print(f"Duplicate rows: {{df.duplicated().sum()}}")
print(f"Columns with all null values: {{df.columns[df.isnull().all()].tolist()}}")
print(f"Columns with single unique value: {{df.columns[df.nunique() == 1].tolist()}}")

# Save summary to file
summary = {{
    "total_rows": len(df),
    "total_columns": len(df.columns),
    "numeric_columns": len(numeric_cols),
    "categorical_columns": len(categorical_cols),
    "missing_data_percentage": (df.isnull().sum().sum() / (len(df) * len(df.columns))) * 100,
    "duplicate_rows": df.duplicated().sum()
}}

with open('output/analysis_summary.json', 'w') as f:
    import json
    json.dump(summary, f, indent=2)

print("\\nAnalysis complete! Check output/ folder for results.")
'''
    
    return code.strip()

def save_csv_metadata(metadata: Dict[str, Any], project_id: str, filename: str) -> str:
    """Save CSV metadata to project folder."""
    metadata_dir = f"projects/{project_id}/metadata"
    os.makedirs(metadata_dir, exist_ok=True)
    
    metadata_file = f"{metadata_dir}/{filename}_metadata.json"
    with open(metadata_file, 'w') as f:
        json.dump(metadata, f, indent=2)
    
    return metadata_file

def create_csv_summary_for_rag(metadata: Dict[str, Any]) -> str:
    """Create a comprehensive summary for vector database indexing."""
    file_info = metadata.get("file_info", {})

    # Start with basic file info
    content_parts = [
        f"CSV File: {file_info.get('filename', 'Unknown')}",
        f"File Path: {file_info.get('file_path', 'Unknown location')}",
        f"Dataset: {file_info.get('total_rows', 0):,} rows × {file_info.get('total_columns', 0)} columns",
        f"File Size: {file_info.get('file_size_mb', 0)} MB",
        "",
        "Columns:"
    ]

    # Add column information
    columns_info = metadata.get("columns", {})
    for col_name, col_info in columns_info.items():
        col_desc = f"- {col_name}: {col_info.get('type', 'unknown')} type"
        if col_info.get("unique_count"):
            col_desc += f", {col_info['unique_count']} unique values"
        if col_info.get("null_count", 0) > 0:
            col_desc += f", {col_info['null_count']} missing"
        content_parts.append(col_desc)

    # Add sample data for context
    if metadata.get("sample_data"):
        content_parts.append("\nSample Data:")
        for i, row in enumerate(metadata["sample_data"][:2]):
            content_parts.append(f"Row {i+1}: {str(row)[:150]}...")

    # Add searchable description if available
    if metadata.get("searchable_description"):
        content_parts.append(f"\nDescription: {metadata['searchable_description']}")

    return "\n".join(content_parts)

def get_csv_metadata_for_vector_db(file_path: str, metadata: Dict[str, Any]) -> Dict[str, Any]:
    """Prepare CSV metadata for vector database storage."""
    return {
        "content": create_csv_summary_for_rag(metadata),
        "metadata": {
            "file_type": "csv_metadata",
            "filename": metadata.get("file_info", {}).get("filename", "unknown"),
            "file_path": file_path,
            "total_rows": metadata.get("file_info", {}).get("total_rows", 0),
            "total_columns": metadata.get("file_info", {}).get("total_columns", 0),
            "columns": metadata.get("column_names", []),
            "source": "csv_analysis"
        }
    }

def process_csv_for_project(file_path: str, project_id: str) -> Dict[str, Any]:
    """Process CSV file and prepare for vector database indexing."""
    try:
        # Analyze CSV
        metadata = analyze_csv(file_path)

        if "error" in metadata:
            return {"error": metadata["error"], "file_path": file_path}

        # Save metadata file
        metadata_file = save_csv_metadata(metadata, project_id, Path(file_path).stem)

        # Prepare for vector DB
        vector_data = get_csv_metadata_for_vector_db(file_path, metadata)

        return {
            "success": True,
            "file_path": file_path,
            "metadata_file": metadata_file,
            "vector_content": vector_data["content"],
            "vector_metadata": vector_data["metadata"],
            "summary": f"Processed CSV: {metadata['file_info']['filename']} ({metadata['file_info']['total_rows']} rows, {metadata['file_info']['total_columns']} columns)"
        }

    except Exception as e:
        return {
            "error": f"Failed to process CSV {file_path}: {str(e)}",
            "file_path": file_path
        }

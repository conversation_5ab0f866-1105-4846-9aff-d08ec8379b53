#!/usr/bin/env python3
"""
Test script for the fantastic reasoning AI with tool chaining capabilities.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_basic_reasoning():
    """Test basic reasoning without tools."""
    print("🧠 Testing Basic Reasoning...")
    
    try:
        from app.agents.BaseAgent import BaseAgent
        
        agent = BaseAgent(project_id="test-reasoning")
        
        response = agent.reply(
            "Explain what you are and what capabilities you have.",
            max_tool_calls=5
        )
        
        print(f"✅ Basic reasoning response:\n{response[:300]}...")
        return True
        
    except Exception as e:
        print(f"❌ Basic reasoning failed: {e}")
        return False

def test_web_search_reasoning():
    """Test web search with follow-up reasoning."""
    print("\n🌐 Testing Web Search + Reasoning...")
    
    try:
        from app.agents.BaseAgent import BaseAgent
        
        agent = BaseAgent(project_id="test-reasoning")
        
        response = agent.reply(
            "Search for the latest developments in AI reasoning systems and summarize the key findings with citations.",
            max_tool_calls=10
        )
        
        print(f"✅ Web search reasoning response:\n{response[:500]}...")
        return True
        
    except Exception as e:
        print(f"❌ Web search reasoning failed: {e}")
        return False

def test_code_execution_reasoning():
    """Test code execution with analysis."""
    print("\n💻 Testing Code Execution + Analysis...")
    
    try:
        from app.agents.BaseAgent import BaseAgent
        
        agent = BaseAgent(project_id="test-reasoning")
        
        response = agent.reply(
            "Analyze the directory structure of this project and identify the main components. Then create a summary of what this codebase does.",
            max_tool_calls=15
        )
        
        print(f"✅ Code execution reasoning response:\n{response[:500]}...")
        return True
        
    except Exception as e:
        print(f"❌ Code execution reasoning failed: {e}")
        return False

def test_project_search_reasoning():
    """Test project search with comprehensive analysis."""
    print("\n🔍 Testing Project Search + Reasoning...")
    
    try:
        from app.agents.BaseAgent import BaseAgent
        
        agent = BaseAgent(project_id="test-reasoning")
        
        response = agent.reply(
            "Search through the project to understand how the BaseAgent works and explain its key features and capabilities.",
            max_tool_calls=12
        )
        
        print(f"✅ Project search reasoning response:\n{response[:500]}...")
        return True
        
    except Exception as e:
        print(f"❌ Project search reasoning failed: {e}")
        return False

def test_multi_tool_reasoning():
    """Test complex reasoning using multiple tools."""
    print("\n🚀 Testing Multi-Tool Reasoning...")
    
    try:
        from app.agents.BaseAgent import BaseAgent
        
        agent = BaseAgent(project_id="test-reasoning")
        
        response = agent.reply(
            """I want to understand this project completely. Please:
            1. Analyze the directory structure 
            2. Search for documentation about the system
            3. Look up current best practices for AI agent systems online
            4. Provide a comprehensive analysis with recommendations""",
            max_tool_calls=15
        )
        
        print(f"✅ Multi-tool reasoning response:\n{response[:500]}...")
        return True
        
    except Exception as e:
        print(f"❌ Multi-tool reasoning failed: {e}")
        return False

def main():
    """Run all reasoning tests."""
    print("🚀 Testing Fantastic Reasoning AI")
    print("=" * 60)
    
    # Check environment
    print(f"LLM Provider: {os.getenv('LLM_PROVIDER', 'Not set')}")
    print(f"Embedding Provider: {os.getenv('EMBEDDING_PROVIDER', 'Not set')}")
    print(f"Google API Key: {'✅ Set' if os.getenv('GOOGLE_API_KEY') else '❌ Not set'}")
    print()
    
    # Run tests
    tests = [
        ("Basic Reasoning", test_basic_reasoning),
        ("Web Search Reasoning", test_web_search_reasoning),
        ("Code Execution Reasoning", test_code_execution_reasoning),
        ("Project Search Reasoning", test_project_search_reasoning),
        ("Multi-Tool Reasoning", test_multi_tool_reasoning)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Reasoning AI Test Results:")
    passed = 0
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"  {status}: {test_name}")
        if result:
            passed += 1
    
    total = len(results)
    if passed == total:
        print(f"\n🎉 All tests passed! ({passed}/{total})")
        print("✅ Fantastic Reasoning AI is working perfectly!")
    else:
        print(f"\n⚠️  Some tests failed. ({passed}/{total} passed)")
        print("Please check the errors above.")
    
    print("\n💡 Key Features Tested:")
    print("  - Multi-turn tool calling (up to 15 calls)")
    print("  - Web search with citation")
    print("  - Code execution with analysis")
    print("  - Project knowledge search")
    print("  - Self-reprompting and reasoning")
    print("  - Final answer enhancement")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

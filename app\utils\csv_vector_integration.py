"""
Integration between CSV handler and vector database for automatic indexing.
"""

import os
from typing import Dict, Any, List
from pathlib import Path
from .csv_handler import process_csv_for_project
from ..managers.DocumentManager import DocumentManager


def add_csv_to_vector_db(file_path: str, project_id: str, document_manager: DocumentManager = None) -> Dict[str, Any]:
    """
    Process CSV file and add its metadata to the vector database.
    
    Args:
        file_path: Path to the CSV file
        project_id: Project ID for organization
        document_manager: Optional DocumentManager instance
    
    Returns:
        Dict with success status and details
    """
    try:
        # Process CSV and get metadata
        result = process_csv_for_project(file_path, project_id)
        
        if not result.get("success"):
            return {
                "success": False,
                "error": result.get("error", "CSV processing failed"),
                "file_path": file_path
            }
        
        # Initialize document manager if not provided
        if document_manager is None:
            document_manager = DocumentManager(project_id=project_id)
        
        # Add to vector database using process_text_content
        vector_result = document_manager.process_text_content(
            content=result["vector_content"],
            filename=f"csv_{Path(file_path).stem}",
            metadata=result["vector_metadata"]
        )
        
        return {
            "success": True,
            "file_path": file_path,
            "metadata_file": result["metadata_file"],
            "vector_result": vector_result.get("status", "unknown"),
            "summary": result["summary"],
            "message": f"✅ CSV metadata indexed in vector database: {Path(file_path).name}"
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": f"Failed to add CSV to vector DB: {str(e)}",
            "file_path": file_path
        }


def process_csv_directory(directory_path: str, project_id: str) -> Dict[str, Any]:
    """
    Process all CSV files in a directory and add them to vector database.
    
    Args:
        directory_path: Path to directory containing CSV files
        project_id: Project ID for organization
    
    Returns:
        Dict with processing results
    """
    try:
        csv_files = []
        for file_path in Path(directory_path).glob("*.csv"):
            csv_files.append(str(file_path))
        
        if not csv_files:
            return {
                "success": True,
                "message": f"No CSV files found in {directory_path}",
                "processed_files": []
            }
        
        # Initialize document manager once
        document_manager = DocumentManager(project_id=project_id)
        
        results = []
        successful = 0
        failed = 0
        
        for csv_file in csv_files:
            print(f"📊 Processing CSV: {Path(csv_file).name}")
            result = add_csv_to_vector_db(csv_file, project_id, document_manager)
            
            if result["success"]:
                successful += 1
                print(f"✅ {result['message']}")
            else:
                failed += 1
                print(f"❌ Failed: {result['error']}")
            
            results.append(result)
        
        return {
            "success": True,
            "total_files": len(csv_files),
            "successful": successful,
            "failed": failed,
            "results": results,
            "summary": f"Processed {len(csv_files)} CSV files: {successful} successful, {failed} failed"
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": f"Failed to process CSV directory: {str(e)}",
            "directory": directory_path
        }


def search_csv_metadata(query: str, project_id: str, k: int = 5) -> List[Dict[str, Any]]:
    """
    Search for CSV files based on their metadata.
    
    Args:
        query: Search query
        project_id: Project ID
        k: Number of results to return
    
    Returns:
        List of search results
    """
    try:
        document_manager = DocumentManager(project_id=project_id)
        
        # Search in vector database
        results = document_manager.vector_search(query, k=k)
        
        # Filter for CSV metadata results
        csv_results = []
        for result in results:
            metadata = result.get("metadata", {})
            if metadata.get("file_type") == "csv_metadata":
                csv_results.append({
                    "filename": metadata.get("filename", "unknown"),
                    "file_path": metadata.get("file_path", "unknown"),
                    "total_rows": metadata.get("total_rows", 0),
                    "total_columns": metadata.get("total_columns", 0),
                    "columns": metadata.get("columns", []),
                    "content": result.get("content", ""),
                    "score": result.get("score", 0.0)
                })
        
        return csv_results
        
    except Exception as e:
        print(f"Error searching CSV metadata: {e}")
        return []


def get_csv_info(filename: str, project_id: str) -> Dict[str, Any]:
    """
    Get detailed information about a specific CSV file.
    
    Args:
        filename: Name of the CSV file
        project_id: Project ID
    
    Returns:
        Dict with CSV information
    """
    try:
        # Search for the specific file
        results = search_csv_metadata(filename, project_id, k=1)
        
        if not results:
            return {
                "found": False,
                "message": f"CSV file '{filename}' not found in project"
            }
        
        csv_info = results[0]
        return {
            "found": True,
            "filename": csv_info["filename"],
            "file_path": csv_info["file_path"],
            "rows": csv_info["total_rows"],
            "columns": csv_info["total_columns"],
            "column_names": csv_info["columns"],
            "description": csv_info["content"]
        }
        
    except Exception as e:
        return {
            "found": False,
            "error": f"Error retrieving CSV info: {str(e)}"
        }

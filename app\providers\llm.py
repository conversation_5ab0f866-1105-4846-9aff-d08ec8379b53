"""Plug-and-play LLM providers."""

import os
import json
from typing import List, Dict, Any, Optional
from abc import ABC, abstractmethod

class LLMProvider(ABC):
    """Base LLM provider interface."""
    
    @abstractmethod
    def chat(self, messages: List[Dict], tools: List[Dict] = None, **kwargs) -> Dict:
        pass

class OpenAIProvider(LLMProvider):
    """OpenAI LLM provider."""
    
    def __init__(self, model: str = "gpt-4o-mini"):
        from openai import OpenAI
        self.client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        self.model = model
    
    def chat(self, messages: List[Dict], tools: List[Dict] = None, **kwargs) -> Dict:
        params = {
            "model": self.model,
            "messages": messages,
            "max_tokens": kwargs.get("max_tokens", 1000),
            "temperature": kwargs.get("temperature", 0.7)
        }
        
        if tools:
            params["tools"] = tools
            params["tool_choice"] = kwargs.get("tool_choice", "auto")
        
        response = self.client.chat.completions.create(**params)
        
        return {
            "content": response.choices[0].message.content,
            "tool_calls": response.choices[0].message.tool_calls,
            "usage": response.usage.dict() if response.usage else None
        }

class GeminiProvider(LLMProvider):
    """Google Gemini LLM provider with function calling support."""

    def __init__(self, model: str = "gemini-1.5-flash"):
        import google.generativeai as genai
        genai.configure(api_key=os.getenv("GOOGLE_API_KEY"))
        self.genai = genai
        self.model_name = model

        # Initialize model with or without tools
        self.model = None

    def _convert_openai_tools_to_gemini(self, tools: List[Dict]) -> List:
        """Convert OpenAI tool format to Gemini function format."""
        if not tools:
            return []

        gemini_functions = []
        for tool in tools:
            if tool.get("type") == "function":
                func = tool["function"]

                # Convert OpenAI schema to Gemini schema
                parameters = func.get("parameters", {})
                gemini_params = {}

                if "properties" in parameters:
                    gemini_params = {
                        "type": "object",
                        "properties": {}
                    }

                    for prop_name, prop_def in parameters["properties"].items():
                        # Convert property types
                        prop_type = prop_def.get("type", "string")
                        if prop_type == "object":
                            prop_type = "object"
                        elif prop_type in ["integer", "number"]:
                            prop_type = "number"
                        else:
                            prop_type = "string"

                        gemini_params["properties"][prop_name] = {
                            "type": prop_type,
                            "description": prop_def.get("description", "")
                        }

                    if "required" in parameters:
                        gemini_params["required"] = parameters["required"]

                gemini_func = {
                    "name": func["name"],
                    "description": func["description"],
                    "parameters": gemini_params
                }
                gemini_functions.append(gemini_func)
        return gemini_functions

    def chat(self, messages: List[Dict], tools: List[Dict] = None, **kwargs) -> Dict:
        try:
            # Convert tools to Gemini format
            gemini_functions = self._convert_openai_tools_to_gemini(tools) if tools else []

            # Initialize model with tools if available
            if gemini_functions:
                self.model = self.genai.GenerativeModel(
                    self.model_name,
                    tools=gemini_functions
                )
            else:
                self.model = self.genai.GenerativeModel(self.model_name)

            # Convert messages to Gemini format
            system_prompt = ""
            user_message = ""

            for msg in messages:
                if msg["role"] == "system":
                    system_prompt = msg["content"]
                elif msg["role"] == "user":
                    user_message = msg["content"]
                # Skip assistant messages for now (Gemini handles conversation differently)

            # Combine system prompt with user message
            if system_prompt and user_message:
                full_prompt = f"{system_prompt}\n\nUser: {user_message}\n\nAssistant:"
            elif user_message:
                full_prompt = user_message
            else:
                full_prompt = "Hello"

            # Generate response
            response = self.model.generate_content(full_prompt)

            # Check for function calls
            tool_calls = None
            if hasattr(response, 'candidates') and response.candidates:
                candidate = response.candidates[0]
                if hasattr(candidate, 'content') and hasattr(candidate.content, 'parts'):
                    for part in candidate.content.parts:
                        if hasattr(part, 'function_call'):
                            # Convert Gemini function call to OpenAI format
                            tool_calls = [{
                                "id": f"call_{hash(part.function_call.name)}",
                                "type": "function",
                                "function": {
                                    "name": part.function_call.name,
                                    "arguments": json.dumps(dict(part.function_call.args))
                                }
                            }]
                            break

            return {
                "content": response.text if response.text else "",
                "tool_calls": tool_calls,
                "usage": None
            }

        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            print(f"Gemini provider error: {error_details}")
            return {
                "content": f"Error: {str(e)}",
                "tool_calls": None,
                "usage": None
            }

class OfflineLLMProvider(LLMProvider):
    """Offline LLM provider using transformers."""
    
    def __init__(self, model: str = "microsoft/DialoGPT-medium"):
        from transformers import AutoTokenizer, AutoModelForCausalLM
        self.tokenizer = AutoTokenizer.from_pretrained(model)
        self.model = AutoModelForCausalLM.from_pretrained(model)
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
    
    def chat(self, messages: List[Dict], tools: List[Dict] = None, **kwargs) -> Dict:
        # Simple conversation format
        conversation = ""
        for msg in messages:
            if msg["role"] == "user":
                conversation += f"User: {msg['content']}\n"
            elif msg["role"] == "assistant":
                conversation += f"Assistant: {msg['content']}\n"
        
        conversation += "Assistant: "
        
        inputs = self.tokenizer.encode(conversation, return_tensors="pt")
        outputs = self.model.generate(
            inputs,
            max_length=inputs.shape[1] + kwargs.get("max_tokens", 100),
            temperature=kwargs.get("temperature", 0.7),
            pad_token_id=self.tokenizer.eos_token_id
        )
        
        response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
        assistant_response = response.split("Assistant: ")[-1]
        
        return {
            "content": assistant_response,
            "tool_calls": None,
            "usage": None
        }

def get_llm_provider(provider: str = None, **kwargs) -> LLMProvider:
    """Factory function to get LLM provider."""
    provider = provider or os.getenv("LLM_PROVIDER", "openai")
    
    providers = {
        "openai": OpenAIProvider,
        "gemini": GeminiProvider,
        # "offline": OfflineLLMProvider
    }
    
    if provider not in providers:
        raise ValueError(f"Unknown provider: {provider}. Available: {list(providers.keys())}")
    
    try:
        return providers[provider](**kwargs)
    except Exception as e:
        print(f"Failed to initialize {provider} LLM: {e}")
        # Fallback to offline if available
        if provider != "offline":
            print("model is not available, stopping")
            # return OfflineLLMProvider(**kwargs)
        raise
